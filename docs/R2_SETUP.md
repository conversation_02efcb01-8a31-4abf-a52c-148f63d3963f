# Cloudflare R2 Setup for Profile Images

This guide explains how to set up Cloudflare R2 Object Storage for storing profile images in the Employee Bio System.

## Why R2?

- **Cost-effective**: No egress fees for data transfer
- **Fast**: Global CDN with edge caching
- **Reliable**: 99.9% uptime SLA
- **S3-compatible**: Uses standard S3 API
- **Automatic compression**: Images are compressed to under 100KB

## Setup Instructions

### 1. Create a Cloudflare Account

1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. Sign up or log in to your account

### 2. Enable R2 Object Storage

1. In the Cloudflare dashboard, navigate to **R2 Object Storage**
2. Click **Create bucket**
3. Choose a bucket name (e.g., `employee-bio-images`)
4. Select a region close to your users
5. Click **Create bucket**

### 3. Configure Public Access (Optional)

If you want images to be publicly accessible:

1. Go to your bucket settings
2. Navigate to **Settings** → **Public access**
3. Enable **Public access** for the bucket
4. Note the public URL format: `https://pub-[hash].r2.dev/[object-key]`

### 4. Create API Tokens

1. Go to **R2 Object Storage** → **Manage R2 API tokens**
2. Click **Create API token**
3. Configure the token:
   - **Token name**: `employee-bio-system`
   - **Permissions**: 
     - `Object:Read`
     - `Object:Write`
     - `Object:Delete`
   - **Bucket**: Select your created bucket
4. Click **Create API token**
5. **Important**: Copy the credentials immediately (they won't be shown again):
   - Access Key ID
   - Secret Access Key
   - Account ID

### 5. Configure Environment Variables

Add the following to your `.env.local` file:

```env
# Cloudflare R2 Object Storage Configuration
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
R2_ACCESS_KEY_ID=your_access_key_id_here
R2_SECRET_ACCESS_KEY=your_secret_access_key_here
R2_BUCKET_NAME=employee-bio-images
R2_PUBLIC_URL=https://your-custom-domain.com
```

### 6. Custom Domain (Optional but Recommended)

For better branding and performance:

1. In your bucket settings, go to **Custom domains**
2. Click **Connect domain**
3. Enter your domain (e.g., `images.yourcompany.com`)
4. Follow the DNS configuration instructions
5. Update `R2_PUBLIC_URL` in your environment variables

## Image Processing Features

The system automatically:

- **Compresses images** to under 100KB
- **Resizes images** to maximum 800x800 pixels
- **Converts to JPEG** for optimal compression
- **Maintains aspect ratio** during resizing
- **Validates file types** (JPEG, PNG, GIF, WebP)
- **Limits file size** to 10MB before compression

## Usage in the Application

Once configured, users can:

1. Upload profile images through the profile edit page
2. See real-time compression statistics
3. Remove their profile images
4. View images instantly through the CDN

## Troubleshooting

### Common Issues

**Error: "R2 is not configured"**
- Check that all environment variables are set correctly
- Restart your development server after adding variables

**Error: "Access denied"**
- Verify your API token has the correct permissions
- Check that the bucket name matches your configuration

**Images not loading**
- Verify the public URL configuration
- Check browser network tab for 404 errors
- Ensure the bucket has public access enabled (if using public URLs)

**Upload fails**
- Check file size (must be under 10MB before compression)
- Verify file type is supported (JPEG, PNG, GIF, WebP)
- Check browser console for detailed error messages

### Testing the Configuration

You can test your R2 setup by:

1. Going to the profile page
2. Clicking edit profile
3. Uploading a test image
4. Checking the browser network tab for successful uploads

## Cost Estimation

R2 pricing (as of 2024):
- **Storage**: $0.015 per GB per month
- **Class A operations** (writes): $4.50 per million requests
- **Class B operations** (reads): $0.36 per million requests
- **No egress fees**

For a typical company with 100 employees:
- Storage: ~100MB = $0.0015/month
- Operations: ~1000 uploads/month = $0.0045
- **Total**: Less than $0.01/month

## Security Best Practices

1. **Use least privilege**: Only grant necessary permissions to API tokens
2. **Rotate tokens**: Regularly update API tokens
3. **Monitor usage**: Set up billing alerts in Cloudflare
4. **Backup**: Consider backing up important images
5. **Access logs**: Enable R2 access logging for audit trails

## Support

For issues with this setup:
1. Check the troubleshooting section above
2. Review Cloudflare R2 documentation
3. Contact your system administrator
4. File an issue in the project repository
