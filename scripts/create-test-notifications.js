#!/usr/bin/env node

/**
 * Create test notifications for development
 */

const mongoose = require('mongoose');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Define schemas directly (like other scripts)
const EmployeeSchema = new mongoose.Schema({
  name: String,
  email: String,
  password: String,
  role: {
    type: String,
    enum: ['employee', 'manager', 'hr', 'admin', 'super_admin'],
    default: 'employee'
  },
  department: String,
  position: String,
  organization: { type: mongoose.Schema.Types.ObjectId, ref: 'Organization' },
  image: String,
  bio: String,
  skills: [String],
  interests: [String],
  location: String,
  linkedIn: String,
  github: String,
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const OrganizationSchema = new mongoose.Schema({
  name: { type: String, required: true },
  domain: { type: String, required: true, unique: true },
  subdomain: { type: String, unique: true, sparse: true },
  description: String,
  industry: String,
  size: {
    type: String,
    enum: ['small', 'medium', 'large', 'enterprise'],
    default: 'small'
  },
  website: String,
  settings: {
    allowPublicProfiles: { type: Boolean, default: true },
    requireEmailVerification: { type: Boolean, default: false },
    enableAchievements: { type: Boolean, default: true },
    enableThanks: { type: Boolean, default: true },
    customBranding: { type: Boolean, default: false },
    maxEmployees: { type: Number, default: 50 }
  },
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'premium', 'enterprise'],
      default: 'free'
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended'],
      default: 'active'
    },
    startDate: { type: Date, default: Date.now },
    endDate: Date,
    features: [String]
  },
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const NotificationSchema = new mongoose.Schema({
  organization: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization',
    required: true
  },
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Employee',
    required: true
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Employee',
    required: true
  },
  type: {
    type: String,
    enum: ['thanks_received', 'achievement_earned', 'mention', 'system'],
    required: true
  },
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  message: {
    type: String,
    required: true,
    maxlength: 1000
  },
  relatedId: {
    type: mongoose.Schema.Types.ObjectId
  },
  relatedType: {
    type: String,
    enum: ['thanks', 'achievement', 'other']
  },
  isRead: {
    type: Boolean,
    default: false
  },
  isEmailSent: {
    type: Boolean,
    default: false
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, { timestamps: true });

const Employee = mongoose.model('Employee', EmployeeSchema);
const Organization = mongoose.model('Organization', OrganizationSchema);
const Notification = mongoose.model('Notification', NotificationSchema);

async function createTestNotifications() {
  try {
    console.log('🔔 Creating test notifications...');

    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find or create demo organization
    let demoOrg = await Organization.findOne({ subdomain: 'demo' });
    if (!demoOrg) {
      console.log('Creating demo organization...');
      demoOrg = await Organization.create({
        name: 'Demo Company',
        domain: 'demo.localhost',
        subdomain: 'demo',
        description: 'Demo organization for testing',
        industry: 'Technology',
        size: 'small'
      });
      console.log('✅ Created demo organization');
    }

    // Get all employees and assign them to the demo organization if needed
    const allEmployees = await Employee.find({});
    for (const emp of allEmployees) {
      if (!emp.organization) {
        emp.organization = demoOrg._id;
        await emp.save();
        console.log(`✅ Assigned ${emp.name} to demo organization`);
      }
    }

    // Get employees in the demo organization
    const employees = await Employee.find({ organization: demoOrg._id });
    if (employees.length < 2) {
      console.error('❌ Need at least 2 employees to create test notifications.');
      process.exit(1);
    }

    console.log(`Found ${employees.length} employees in demo organization`);

    // Clear existing notifications for demo org
    await Notification.deleteMany({ organization: demoOrg._id });
    console.log('🗑️ Cleared existing notifications');

    // Create test notifications
    const notifications = [];
    
    for (let i = 0; i < employees.length; i++) {
      const recipient = employees[i];
      const sender = employees[(i + 1) % employees.length]; // Next employee as sender
      
      // Create a thanks notification
      notifications.push({
        organization: demoOrg._id,
        recipient: recipient._id,
        sender: sender._id,
        type: 'thanks_received',
        title: '💝 You received thanks!',
        message: `${sender.name} thanked you for your excellent teamwork on the recent project.`,
        relatedType: 'thanks',
        isRead: false,
        isEmailSent: false,
        metadata: {
          thanksCategory: 'teamwork',
          senderName: sender.name
        }
      });

      // Create an achievement notification
      if (i % 2 === 0) {
        notifications.push({
          organization: demoOrg._id,
          recipient: recipient._id,
          sender: recipient._id, // Self-notification for achievements
          type: 'achievement_earned',
          title: '🏆 Achievement Unlocked!',
          message: `Congratulations! You've earned the "Team Player" achievement.`,
          relatedType: 'achievement',
          isRead: false,
          isEmailSent: false,
          metadata: {
            achievementTitle: 'Team Player',
            achievementType: 'collaboration'
          }
        });
      }

      // Create a system notification
      if (i % 3 === 0) {
        notifications.push({
          organization: demoOrg._id,
          recipient: recipient._id,
          sender: sender._id,
          type: 'system',
          title: '📢 System Update',
          message: 'Welcome to the new notification system! You can now receive real-time updates.',
          isRead: false,
          isEmailSent: false,
          metadata: {
            updateType: 'feature_announcement'
          }
        });
      }
    }

    // Insert all notifications
    await Notification.insertMany(notifications);
    console.log(`✅ Created ${notifications.length} test notifications`);

    // Show summary
    const notificationCounts = await Notification.aggregate([
      { $match: { organization: demoOrg._id } },
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]);

    console.log('\n📊 Notification Summary:');
    notificationCounts.forEach(item => {
      console.log(`  ${item._id}: ${item.count}`);
    });

    console.log('\n🎯 Test the notifications by:');
    console.log('1. Sign in to the application');
    console.log('2. Look for the notification bell icon in the navigation');
    console.log('3. Click the bell to see your notifications');
    console.log('4. The bell should show a red badge with the unread count');

  } catch (error) {
    console.error('❌ Error creating test notifications:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  createTestNotifications()
    .then(() => {
      console.log('🎉 Test notifications created successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Failed to create test notifications:', error);
      process.exit(1);
    });
}

module.exports = createTestNotifications;
