#!/usr/bin/env node

/**
 * Quick test to create a single notification
 */

const mongoose = require('mongoose');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

async function quickTest() {
  try {
    console.log('🔔 Quick notification test...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get collections directly
    const db = mongoose.connection.db;
    
    // Find any employee
    const employees = await db.collection('employees').find({}).limit(2).toArray();
    console.log(`Found ${employees.length} employees`);
    
    if (employees.length < 2) {
      console.log('❌ Need at least 2 employees');
      return;
    }

    // Find or create organization
    let org = await db.collection('organizations').findOne({ subdomain: 'demo' });
    if (!org) {
      const result = await db.collection('organizations').insertOne({
        name: 'Demo Company',
        domain: 'demo.localhost',
        subdomain: 'demo',
        description: 'Demo organization for testing',
        industry: 'Technology',
        size: 'small',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        settings: {
          allowPublicProfiles: true,
          requireEmailVerification: false,
          enableAchievements: true,
          enableThanks: true,
          customBranding: false,
          maxEmployees: 50
        },
        subscription: {
          plan: 'free',
          status: 'active',
          startDate: new Date(),
          features: []
        }
      });
      org = { _id: result.insertedId };
      console.log('✅ Created demo organization');
    }

    // Update employees to have organization
    await db.collection('employees').updateMany(
      { organization: { $exists: false } },
      { $set: { organization: org._id } }
    );
    console.log('✅ Updated employees with organization');

    // Clear existing notifications
    await db.collection('notifications').deleteMany({ organization: org._id });
    console.log('🗑️ Cleared existing notifications');

    // Create a test notification
    const notification = {
      organization: org._id,
      recipient: employees[0]._id,
      sender: employees[1]._id,
      type: 'thanks_received',
      title: '💝 You received thanks!',
      message: `${employees[1].name || 'A colleague'} thanked you for your excellent work!`,
      relatedType: 'thanks',
      isRead: false,
      isEmailSent: false,
      metadata: {
        thanksCategory: 'teamwork',
        senderName: employees[1].name || 'Unknown'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.collection('notifications').insertOne(notification);
    console.log('✅ Created test notification');

    // Show summary
    const notificationCount = await db.collection('notifications').countDocuments({ organization: org._id });
    console.log(`📊 Total notifications: ${notificationCount}`);

    console.log('\n🎯 Test the notification by:');
    console.log('1. Sign in to the application');
    console.log('2. Look for the notification bell icon');
    console.log('3. The bell should show a red badge with "1"');
    console.log('4. Click the bell to see the notification');

  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  quickTest()
    .then(() => {
      console.log('🎉 Quick test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = quickTest;
