{"name": "employee-bio-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "migrate:roles": "node scripts/migrate-roles.js", "seed:sample": "node scripts/seed-sample-data.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "@headlessui/react": "^2.2.4", "@next-auth/mongodb-adapter": "^1.1.3", "bcryptjs": "^3.0.2", "framer-motion": "^12.18.1", "lucide-react": "^0.522.0", "mongoose": "^8.16.0", "next": "15.3.4", "next-auth": "^4.24.11", "openai": "^5.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.4", "sharp": "^0.34.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^2.4.6", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}