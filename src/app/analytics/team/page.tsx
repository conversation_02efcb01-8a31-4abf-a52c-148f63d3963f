'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  Heart,
  Trophy,
  TrendingUp,
  Award,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Activity,
  Sparkles,
  RefreshCw
} from 'lucide-react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON><PERSON>,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import Navigation from '@/components/Navigation';
import { hasPermission } from '@/lib/roles';
import { UserRole } from '@/types/global';

interface TeamStats {
  totalMembers: number;
  thanksGiven: number;
  thanksReceived: number;
  achievements: number;
  topPerformers: {
    name: string;
    thanksCount: number;
    achievementCount: number;
  }[];
  departmentBreakdown: {
    department: string;
    count: number;
  }[];
}

export default function TeamAnalytics() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState<TeamStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  const COLORS = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#6366F1'];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    const userRole = session.user?.role as UserRole;
    if (!hasPermission(userRole, 'view_team_analytics')) {
      router.push('/');
      return;
    }

    fetchTeamStats();
  }, [session, status, router]);

  const fetchTeamStats = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const response = await fetch('/api/analytics/team');
      if (!response.ok) {
        throw new Error('Failed to fetch team analytics');
      }

      const data = await response.json();
      setStats(data.stats);
      setError('');

    } catch (error) {
      console.error('Error fetching team stats:', error);
      setError('Failed to load team analytics');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    fetchTeamStats(true);
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <Navigation />
        <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col items-center justify-center h-64"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full mb-4"
            />
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="text-gray-600 font-medium"
            >
              Loading team analytics...
            </motion.p>
          </motion.div>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Navigation />

      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Team Analytics
              </h1>
              <p className="mt-2 text-gray-600 text-lg">
                Insights and metrics for your team's performance and engagement
              </p>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center space-x-2 px-4 py-2 bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 border border-gray-200"
            >
              <RefreshCw size={18} className={`${refreshing ? 'animate-spin' : ''} text-gray-600`} />
              <span className="text-gray-700 font-medium">Refresh</span>
            </motion.button>
          </div>
        </motion.div>

        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mb-6 bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-xl shadow-sm"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Activity className="h-5 w-5 text-red-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium">{error}</p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {stats && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="space-y-8"
            >
              {/* Overview Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[
                  {
                    title: 'Team Members',
                    value: stats.totalMembers,
                    icon: Users,
                    color: 'from-blue-500 to-blue-600',
                    bgColor: 'bg-blue-50',
                    change: '+2 this month'
                  },
                  {
                    title: 'Thanks Given',
                    value: stats.thanksGiven,
                    icon: Heart,
                    color: 'from-pink-500 to-rose-600',
                    bgColor: 'bg-pink-50',
                    change: '+12 this week'
                  },
                  {
                    title: 'Thanks Received',
                    value: stats.thanksReceived,
                    icon: Sparkles,
                    color: 'from-purple-500 to-purple-600',
                    bgColor: 'bg-purple-50',
                    change: '+8 this week'
                  },
                  {
                    title: 'Achievements',
                    value: stats.achievements,
                    icon: Trophy,
                    color: 'from-yellow-500 to-orange-600',
                    bgColor: 'bg-yellow-50',
                    change: '+3 this month'
                  }
                ].map((stat, index) => (
                  <motion.div
                    key={stat.title}
                    variants={itemVariants}
                    whileHover={{ scale: 1.02, y: -5 }}
                    className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
                  >
                    <div className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                            <stat.icon className={`w-6 h-6 bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`} />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                            <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center text-green-600 text-sm">
                            <TrendingUp size={16} className="mr-1" />
                            <span className="font-medium">{stat.change}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Charts Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Top Performers */}
                <motion.div
                  variants={itemVariants}
                  className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
                >
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg">
                        <Award className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Top Performers</h3>
                        <p className="text-sm text-gray-600">Team members with the most recognition</p>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="space-y-4">
                      {stats.topPerformers.map((performer, index) => (
                        <motion.div
                          key={performer.name}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          whileHover={{ scale: 1.02 }}
                          className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 hover:from-primary-50 hover:to-primary-100 transition-all duration-200"
                        >
                          <div className="flex items-center space-x-4">
                            <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-white ${
                              index === 0 ? 'bg-gradient-to-r from-yellow-400 to-orange-500' :
                              index === 1 ? 'bg-gradient-to-r from-gray-400 to-gray-500' :
                              'bg-gradient-to-r from-orange-400 to-red-500'
                            }`}>
                              #{index + 1}
                            </div>
                            <div>
                              <div className="font-semibold text-gray-900">{performer.name}</div>
                              <div className="text-sm text-gray-600">Team Member</div>
                            </div>
                          </div>
                          <div className="flex space-x-6 text-right">
                            <div>
                              <div className="text-lg font-bold text-pink-600">{performer.thanksCount}</div>
                              <div className="text-xs text-gray-500">Thanks</div>
                            </div>
                            <div>
                              <div className="text-lg font-bold text-yellow-600">{performer.achievementCount}</div>
                              <div className="text-xs text-gray-500">Achievements</div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>

                {/* Department Breakdown Chart */}
                <motion.div
                  variants={itemVariants}
                  className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
                >
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg">
                        <PieChart className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Department Distribution</h3>
                        <p className="text-sm text-gray-600">Team member breakdown by department</p>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="h-64 mb-6">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsPieChart>
                          <Pie
                            data={stats.departmentBreakdown}
                            cx="50%"
                            cy="50%"
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="count"
                            label={({ department, count }) => `${department}: ${count}`}
                          >
                            {stats.departmentBreakdown.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="space-y-3">
                      {stats.departmentBreakdown.map((dept, index) => (
                        <motion.div
                          key={dept.department}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="flex items-center justify-between p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-200"
                        >
                          <div className="flex items-center space-x-3">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: COLORS[index % COLORS.length] }}
                            />
                            <span className="font-medium text-gray-900">{dept.department}</span>
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="w-24 bg-gray-200 rounded-full h-2">
                              <motion.div
                                initial={{ width: 0 }}
                                animate={{ width: `${(dept.count / stats.totalMembers) * 100}%` }}
                                transition={{ duration: 1, delay: index * 0.2 }}
                                className="h-2 rounded-full"
                                style={{ backgroundColor: COLORS[index % COLORS.length] }}
                              />
                            </div>
                            <span className="text-sm font-semibold text-gray-700 w-8 text-right">{dept.count}</span>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
