import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Thanks from '@/models/Thanks';
import Employee from '@/models/Employee';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get organization context
    const orgContext = await getOrganizationFromRequest(request);
    if (!orgContext) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Get current user's employee record
    const currentEmployee = await Employee.findOne({
      email: session.user.email,
      organization: orgContext.organization._id
    });

    if (!currentEmployee) {
      return NextResponse.json(
        { error: 'Employee record not found' },
        { status: 404 }
      );
    }

    const { id } = await params;
    const body = await request.json();
    const { isPublic } = body;

    if (typeof isPublic !== 'boolean') {
      return NextResponse.json(
        { error: 'isPublic must be a boolean value' },
        { status: 400 }
      );
    }

    // Find the thanks message
    const thanks = await Thanks.findOne({
      _id: id,
      organization: orgContext.organization._id
    });

    if (!thanks) {
      return NextResponse.json(
        { error: 'Thanks message not found' },
        { status: 404 }
      );
    }

    // Check if the current user is the recipient of the thanks
    if (!thanks.toEmployee.equals(currentEmployee._id)) {
      return NextResponse.json(
        { error: 'You can only change the privacy of thanks messages directed to you' },
        { status: 403 }
      );
    }

    // Update the privacy status
    thanks.isPublic = isPublic;
    await thanks.save();

    // Return the updated thanks with populated fields
    const updatedThanks = await Thanks.findById(thanks._id)
      .populate('fromEmployee', 'name email image department position')
      .populate('toEmployee', 'name email image department position')
      .select('-__v');

    return NextResponse.json({
      message: `Thanks message is now ${isPublic ? 'public' : 'private'}`,
      thanks: updatedThanks
    });

  } catch (error) {
    console.error('Error updating thanks privacy:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
