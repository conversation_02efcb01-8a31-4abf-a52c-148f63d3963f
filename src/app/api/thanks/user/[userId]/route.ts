import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Thanks from '@/models/Thanks';
import Employee from '@/models/Employee';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Allow both authenticated and unauthenticated users to view public thanks
    const session = await getServerSession(authOptions);

    await dbConnect();

    const { userId } = await params;
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');

    // Find the target employee and their organization
    const targetEmployee = await Employee.findById(userId);
    if (!targetEmployee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Get current user's employee record if authenticated
    let currentEmployee = null;
    if (session?.user?.email) {
      // Try to find current user in the same organization as target employee
      currentEmployee = await Employee.findOne({
        email: session.user.email,
        organization: targetEmployee.organization
      });
      
      // If not found in target's organization, try to find in any organization
      if (!currentEmployee) {
        currentEmployee = await Employee.findOne({
          email: session.user.email
        });
      }
    }

    // Build privacy filter for user-specific thanks
    let privacyFilter: any;
    if (currentEmployee) {
      // Authenticated users can see:
      // 1. All public thanks for the target user
      // 2. Private thanks for the target user where current user is the sender
      // 3. Private thanks for the target user where current user is the recipient (i.e., viewing own thanks)
      privacyFilter = {
        $or: [
          { isPublic: true },
          { 
            isPublic: false,
            $or: [
              { fromEmployee: currentEmployee._id }, // Current user sent private thanks to target
              { toEmployee: currentEmployee._id }     // Current user is viewing their own private thanks
            ]
          }
        ]
      };
    } else {
      // Anonymous users can only see public thanks
      privacyFilter = { isPublic: true };
    }

    let query: any = {
      ...privacyFilter,
      toEmployee: userId,
      // Filter by target employee's organization for security
      organization: targetEmployee.organization
    };

    const thanks = await Thanks.find(query)
      .populate('fromEmployee', 'name email image department position')
      .populate('toEmployee', 'name email image department position')
      .select('-__v')
      .limit(limit)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Thanks.countDocuments(query);

    return NextResponse.json({
      thanks,
      targetEmployee: {
        _id: targetEmployee._id,
        name: targetEmployee.name,
        email: targetEmployee.email,
        image: targetEmployee.image,
        department: targetEmployee.department,
        position: targetEmployee.position
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching user thanks:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
