import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import CloudflareR2Service from '@/lib/cloudflare-images';
import { compressImageServer } from '@/lib/image-compression';

// Configure the API route to handle file uploads
export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get the current employee
    const employee = await Employee.findOne({ email: session.user.email });
    if (!employee) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 });
    }

    // Parse the form data
    const formData = await request.formData();
    const file = formData.get('image') as File;

    if (!file) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }

    // Validate file type and size
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.' },
        { status: 400 }
      );
    }

    // Check initial file size (10MB max before compression)
    const maxSizeMB = 10;
    if (file.size > maxSizeMB * 1024 * 1024) {
      return NextResponse.json(
        { error: `File too large. Please upload an image smaller than ${maxSizeMB}MB.` },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const inputBuffer = Buffer.from(arrayBuffer);

    // Compress the image to ensure it's under 100KB
    let compressedResult;
    try {
      compressedResult = await compressImageServer(inputBuffer, {
        maxSizeKB: 100,
        maxWidth: 800,
        maxHeight: 800,
        quality: 85,
        format: 'jpeg', // Convert all to JPEG for consistency and smaller size
      });
    } catch (compressionError) {
      console.error('Compression error:', compressionError);
      return NextResponse.json(
        { error: 'Failed to compress image. Please try a different image.' },
        { status: 500 }
      );
    }

    // Generate filename
    const timestamp = Date.now();
    const filename = `profile-${employee._id}-${timestamp}.jpg`;

    // Upload to Cloudflare R2
    const r2Service = new CloudflareR2Service();
    let imageUrl;
    
    try {
      imageUrl = await r2Service.uploadImage(
        compressedResult.buffer,
        filename,
        {
          employeeId: employee._id.toString(),
          type: 'profile',
          originalName: file.name,
          compressedSize: compressedResult.size.toString(),
        }
      );
    } catch (uploadError) {
      console.error('R2 upload error:', uploadError);
      return NextResponse.json(
        { error: 'Failed to upload image. Please try again.' },
        { status: 500 }
      );
    }

    // Delete old image from R2 if it exists
    if (employee.image) {
      try {
        const oldImageKey = r2Service.extractImageKey(employee.image);
        if (oldImageKey) {
          await r2Service.deleteImage(oldImageKey);
        }
      } catch (deleteError) {
        console.error('Failed to delete old image:', deleteError);
        // Don't fail the request if old image deletion fails
      }
    }

    // Update employee record with new image URL
    const updatedEmployee = await Employee.findByIdAndUpdate(
      employee._id,
      { 
        image: imageUrl,
        updatedAt: new Date(),
      },
      { new: true }
    ).select('-password');

    return NextResponse.json({
      success: true,
      imageUrl,
      employee: updatedEmployee,
      metadata: {
        originalSize: file.size,
        compressedSize: compressedResult.size,
        compressionRatio: Math.round((1 - compressedResult.size / file.size) * 100),
        dimensions: {
          width: compressedResult.width,
          height: compressedResult.height,
        },
      },
    });

  } catch (error) {
    console.error('Profile image upload error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get the current employee
    const employee = await Employee.findOne({ email: session.user.email });
    if (!employee) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 });
    }

    if (!employee.image) {
      return NextResponse.json({ error: 'No profile image to delete' }, { status: 400 });
    }

    // Delete image from R2
    const r2Service = new CloudflareR2Service();
    try {
      const imageKey = r2Service.extractImageKey(employee.image);
      if (imageKey) {
        await r2Service.deleteImage(imageKey);
      }
    } catch (deleteError) {
      console.error('Failed to delete image from R2:', deleteError);
      // Continue with database update even if R2 deletion fails
    }

    // Remove image URL from employee record
    const updatedEmployee = await Employee.findByIdAndUpdate(
      employee._id,
      { 
        $unset: { image: 1 },
        updatedAt: new Date(),
      },
      { new: true }
    ).select('-password');

    return NextResponse.json({
      success: true,
      employee: updatedEmployee,
    });

  } catch (error) {
    console.error('Profile image deletion error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
