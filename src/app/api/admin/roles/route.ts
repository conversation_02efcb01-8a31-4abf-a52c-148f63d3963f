import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import { withAuth } from '@/lib/middleware/auth';
import { getAllRoles, ROLE_DISPLAY_NAMES, ROLE_DESCRIPTIONS, getRolePermissions } from '@/lib/roles';

/**
 * GET /api/admin/roles
 * Get all available roles and their information
 */
export const GET = withAuth(async (request: NextRequest) => {
  try {
    const roles = getAllRoles().map(role => ({
      value: role,
      label: ROLE_DISPLAY_NAMES[role],
      description: ROLE_DESCRIPTIONS[role],
      permissions: getRolePermissions(role),
    }));

    return NextResponse.json({
      roles,
      message: 'Roles retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching roles:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireAdminOrSuperAdmin: true });
