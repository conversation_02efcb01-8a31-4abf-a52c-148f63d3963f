import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import Organization from '@/models/Organization';
import { withAuth } from '@/lib/middleware/auth';

/**
 * POST /api/admin/migrate-users
 * Migrate existing users to have organization association
 */
export const POST = withAuth(async (request: NextRequest) => {
  try {
    await dbConnect();

    // Find or create demo organization
    let demoOrg = await Organization.findOne({ subdomain: 'demo' });
    
    if (!demoOrg) {
      demoOrg = new Organization({
        name: 'Demo Company',
        domain: 'demo.localhost',
        subdomain: 'demo',
        description: 'Demo organization for development and testing',
        industry: 'Technology',
        size: 'small',
        settings: {
          allowPublicProfiles: true,
          requireEmailVerification: false,
          enableAchievements: true,
          enableThanks: true,
          customBranding: false,
          maxEmployees: 100,
        },
        subscription: {
          plan: 'premium',
          status: 'active',
          startDate: new Date(),
          features: ['thanks', 'achievements', 'analytics', 'custom_branding'],
        },
        isActive: true,
      });
      
      await demoOrg.save();
      console.log('Created demo organization');
    }

    // Find users without organization
    const usersWithoutOrg = await Employee.find({
      $or: [
        { organization: { $exists: false } },
        { organization: null }
      ]
    });

    console.log(`Found ${usersWithoutOrg.length} users without organization`);

    // Update users to have the demo organization
    const updateResult = await Employee.updateMany(
      {
        $or: [
          { organization: { $exists: false } },
          { organization: null }
        ]
      },
      {
        $set: { organization: demoOrg._id }
      }
    );

    console.log(`Updated ${updateResult.modifiedCount} users with demo organization`);

    return NextResponse.json({
      message: 'User migration completed successfully',
      organizationId: demoOrg._id,
      organizationName: demoOrg.name,
      usersFound: usersWithoutOrg.length,
      usersUpdated: updateResult.modifiedCount
    });

  } catch (error) {
    console.error('Error migrating users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireAdminOrSuperAdmin: true });
