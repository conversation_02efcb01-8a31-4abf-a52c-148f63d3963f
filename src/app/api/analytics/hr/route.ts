import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import Thanks from '@/models/Thanks';
import Achievement from '@/models/Achievement';
import { withAuth, getAuthenticatedUser } from '@/lib/middleware/auth';

/**
 * GET /api/analytics/hr
 * Get HR analytics data
 */
export const GET = withAuth(async (request: NextRequest) => {
  try {
    await dbConnect();

    const user = getAuthenticatedUser(request);

    // Get current user's full employee record to access organization
    const currentEmployee = await Employee.findOne({ email: user.email })
      .populate('organization')
      .lean();

    if (!currentEmployee) {
      return NextResponse.json(
        { error: 'Employee record not found' },
        { status: 404 }
      );
    }

    // Build filter based on user role and organization
    let orgFilter: any = {};

    // CRITICAL: Filter by organization for all roles except super_admin
    if (user.role !== 'super_admin') {
      orgFilter.organization = currentEmployee.organization._id;
    }

    // Get total employees (filtered by organization)
    const totalEmployees = await Employee.countDocuments(orgFilter);

    // Get new hires this month (filtered by organization)
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const newHiresThisMonth = await Employee.countDocuments({
      ...orgFilter,
      createdAt: { $gte: startOfMonth }
    });

    // Get total departments (filtered by organization)
    const departmentData = await Employee.aggregate([
      {
        $match: orgFilter
      },
      {
        $group: {
          _id: '$department',
          count: { $sum: 1 }
        }
      },
      {
        $match: { _id: { $ne: null } }
      }
    ]);
    const totalDepartments = departmentData.length;

    // Calculate average tenure (simplified - using createdAt as join date, filtered by organization)
    const employees = await Employee.find(orgFilter, 'createdAt').lean();
    const now = new Date();
    const totalTenureMonths = employees.reduce((sum, emp) => {
      const months = (now.getTime() - emp.createdAt.getTime()) / (1000 * 60 * 60 * 24 * 30);
      return sum + months;
    }, 0);
    const averageTenureMonths = totalTenureMonths / employees.length;
    const averageTenure = `${(averageTenureMonths / 12).toFixed(1)} years`;

    // Get employees by role (filtered by organization)
    const employeesByRole = await Employee.aggregate([
      {
        $match: orgFilter
      },
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          role: {
            $switch: {
              branches: [
                { case: { $eq: ['$_id', 'employee'] }, then: 'Employee' },
                { case: { $eq: ['$_id', 'manager'] }, then: 'Manager' },
                { case: { $eq: ['$_id', 'hr'] }, then: 'HR' },
                { case: { $eq: ['$_id', 'admin'] }, then: 'Admin' }
              ],
              default: 'Unknown'
            }
          },
          count: 1,
          _id: 0
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get department statistics (filtered by organization)
    const departmentStats = await Employee.aggregate([
      {
        $match: orgFilter
      },
      {
        $group: {
          _id: '$department',
          employeeCount: { $sum: 1 }
        }
      },
      {
        $match: { _id: { $ne: null } }
      },
      {
        $lookup: {
          from: 'employees',
          let: { dept: '$_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$department', '$$dept'] } } }
          ],
          as: 'deptEmployees'
        }
      },
      {
        $lookup: {
          from: 'thanks',
          let: { empIds: '$deptEmployees._id' },
          pipeline: [
            { $match: { $expr: { $in: ['$toEmployee', '$$empIds'] } } }
          ],
          as: 'thanks'
        }
      },
      {
        $lookup: {
          from: 'achievements',
          let: { empIds: '$deptEmployees._id' },
          pipeline: [
            { $match: { $expr: { $in: ['$employee', '$$empIds'] } } }
          ],
          as: 'achievements'
        }
      },
      {
        $project: {
          department: '$_id',
          employeeCount: 1,
          thanksCount: { $size: '$thanks' },
          achievementCount: { $size: '$achievements' },
          _id: 0
        }
      },
      {
        $sort: { employeeCount: -1 }
      }
    ]);

    // Get recent activity (last 10 activities, filtered by organization)
    const recentEmployees = await Employee.find(orgFilter)
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name department createdAt')
      .lean();

    // Get organization employee IDs for filtering thanks and achievements
    const orgEmployeeIds = await Employee.find(orgFilter, '_id').lean();
    const orgEmployeeIdList = orgEmployeeIds.map(emp => emp._id);

    const recentThanks = await Thanks.find({
      $or: [
        { toEmployee: { $in: orgEmployeeIdList } },
        { fromEmployee: { $in: orgEmployeeIdList } }
      ]
    })
      .sort({ createdAt: -1 })
      .limit(3)
      .populate('toEmployee', 'name')
      .populate('fromEmployee', 'name')
      .select('message createdAt toEmployee fromEmployee')
      .lean();

    const recentAchievements = await Achievement.find({
      employee: { $in: orgEmployeeIdList }
    })
      .sort({ createdAt: -1 })
      .limit(3)
      .populate('employee', 'name')
      .select('title employee createdAt')
      .lean();

    // Combine and sort recent activities
    const recentActivity = [
      ...recentEmployees.map(emp => ({
        type: 'hire' as const,
        description: `${emp.name} joined ${emp.department || 'the company'}`,
        date: emp.createdAt.toISOString().split('T')[0]
      })),
      ...recentThanks.map(thanks => ({
        type: 'thanks' as const,
        description: `${(thanks.toEmployee as any)?.name} received thanks from ${(thanks.fromEmployee as any)?.name}`,
        date: thanks.createdAt.toISOString().split('T')[0]
      })),
      ...recentAchievements.map(achievement => ({
        type: 'achievement' as const,
        description: `${(achievement.employee as any)?.name} earned "${achievement.title}" achievement`,
        date: achievement.createdAt.toISOString().split('T')[0]
      }))
    ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 10);

    const stats = {
      totalEmployees,
      newHiresThisMonth,
      totalDepartments,
      averageTenure,
      employeesByRole,
      departmentStats,
      recentActivity
    };

    return NextResponse.json({
      stats,
      message: 'HR analytics retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching HR analytics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { permissions: ['view_hr_analytics'] });
