import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import Thanks from '@/models/Thanks';
import Achievement from '@/models/Achievement';
import { withAuth, getAuthenticatedUser } from '@/lib/middleware/auth';

/**
 * GET /api/analytics/team
 * Get team analytics data for managers
 */
export const GET = withAuth(async (request: NextRequest) => {
  try {
    await dbConnect();

    const user = getAuthenticatedUser(request);

    // Get current user's full employee record to access organization
    const currentEmployee = await Employee.findOne({ email: user.email })
      .populate('organization')
      .lean();

    if (!currentEmployee) {
      return NextResponse.json(
        { error: 'Employee record not found' },
        { status: 404 }
      );
    }

    // Build filter based on user role and organization
    let teamFilter: any = {};

    // CRITICAL: Filter by organization for all roles except super_admin
    if (user.role !== 'super_admin') {
      teamFilter.organization = currentEmployee.organization._id;
    }

    if (user.role === 'manager') {
      // Managers can only see their department within their organization
      if (!user.department) {
        return NextResponse.json(
          { error: 'Manager must have a department assigned' },
          { status: 400 }
        );
      }
      teamFilter.department = user.department;
    }
    // HR and Admin can see all teams within their organization
    // Super Admin can see all teams across all organizations

    // Get team members
    const teamMembers = await Employee.find(teamFilter)
      .select('name email department position role createdAt organization')
      .lean();

    const teamMemberIds = teamMembers.map(member => member._id);

    // Get thanks statistics (filtered by team members from same organization)
    const thanksGiven = await Thanks.countDocuments({
      fromEmployee: { $in: teamMemberIds }
    });

    const thanksReceived = await Thanks.countDocuments({
      toEmployee: { $in: teamMemberIds }
    });

    // Get achievements count (filtered by team members from same organization)
    const achievements = await Achievement.countDocuments({
      employee: { $in: teamMemberIds }
    });

    // Get top performers (by thanks received)
    const topPerformersData = await Thanks.aggregate([
      {
        $match: { toEmployee: { $in: teamMemberIds } }
      },
      {
        $group: {
          _id: '$toEmployee',
          thanksCount: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'employees',
          localField: '_id',
          foreignField: '_id',
          as: 'employee'
        }
      },
      {
        $unwind: '$employee'
      },
      {
        $lookup: {
          from: 'achievements',
          localField: '_id',
          foreignField: 'employee',
          as: 'achievements'
        }
      },
      {
        $project: {
          name: '$employee.name',
          thanksCount: 1,
          achievementCount: { $size: '$achievements' }
        }
      },
      {
        $sort: { thanksCount: -1 }
      },
      {
        $limit: 5
      }
    ]);

    // Get department breakdown
    const departmentBreakdown = await Employee.aggregate([
      {
        $match: teamFilter
      },
      {
        $group: {
          _id: '$department',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          department: { $ifNull: ['$_id', 'Unassigned'] },
          count: 1,
          _id: 0
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    const stats = {
      totalMembers: teamMembers.length,
      thanksGiven,
      thanksReceived,
      achievements,
      topPerformers: topPerformersData.map(performer => ({
        name: performer.name,
        thanksCount: performer.thanksCount,
        achievementCount: performer.achievementCount
      })),
      departmentBreakdown: departmentBreakdown.map(dept => ({
        department: dept.department,
        count: dept.count
      }))
    };

    return NextResponse.json({
      stats,
      message: 'Team analytics retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching team analytics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { permissions: ['view_team_analytics'] });
