import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';

// GET /api/debug/user - Get current user info for debugging
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ 
        error: 'Not authenticated',
        session: null,
        user: null 
      }, { status: 401 });
    }

    await dbConnect();

    const employee = await Employee.findOne({ email: session.user.email })
      .populate('organization', 'name domain subdomain')
      .select('-password');

    return NextResponse.json({
      session: {
        user: session.user,
        expires: session.expires
      },
      employee: employee ? {
        id: employee._id,
        name: employee.name,
        email: employee.email,
        role: employee.role,
        organization: employee.organization,
        createdAt: employee.createdAt
      } : null,
      debug: {
        timestamp: new Date().toISOString(),
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
      }
    });

  } catch (error) {
    console.error('Debug user error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
