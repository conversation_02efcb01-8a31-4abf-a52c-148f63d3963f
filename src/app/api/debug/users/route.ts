import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import Organization from '@/models/Organization';

/**
 * GET /api/debug/users
 * Debug endpoint to check user and organization state
 */
export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    // Get all users
    const allUsers = await Employee.find({}).select('name email organization role').lean();
    
    // Get all organizations
    const allOrganizations = await Organization.find({}).select('name domain subdomain').lean();
    
    // Count users without organization
    const usersWithoutOrg = await Employee.countDocuments({
      $or: [
        { organization: { $exists: false } },
        { organization: null }
      ]
    });

    // Count users with organization
    const usersWithOrg = await Employee.countDocuments({
      organization: { $exists: true, $ne: null }
    });

    return NextResponse.json({
      totalUsers: allUsers.length,
      usersWithOrganization: usersWithOrg,
      usersWithoutOrganization: usersWithoutOrg,
      totalOrganizations: allOrganizations.length,
      users: allUsers,
      organizations: allOrganizations
    });

  } catch (error) {
    console.error('Error in debug endpoint:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
