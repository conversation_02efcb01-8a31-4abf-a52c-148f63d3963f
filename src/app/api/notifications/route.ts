import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import NotificationService from '@/lib/notifications';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get organization context
    const orgContext = await getOrganizationFromRequest(request);
    if (!orgContext) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Get current user within organization
    const currentUser = await Employee.findOne({
      email: session.user.email,
      organization: orgContext.organization._id
    });
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    const unreadOnly = searchParams.get('unreadOnly') === 'true';
    const type = searchParams.get('type') || undefined;

    const result = await NotificationService.getUserNotifications(currentUser._id, {
      organizationId: orgContext.organization._id,
      limit,
      page,
      unreadOnly,
      type
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get organization context
    const orgContext = await getOrganizationFromRequest(request);
    if (!orgContext) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Get current user within organization
    const currentUser = await Employee.findOne({
      email: session.user.email,
      organization: orgContext.organization._id
    });
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const body = await request.json();
    const { action, notificationId } = body;

    if (action === 'markAsRead' && notificationId) {
      const success = await NotificationService.markAsRead(notificationId, currentUser._id);
      if (success) {
        return NextResponse.json({ success: true });
      } else {
        return NextResponse.json({ error: 'Notification not found' }, { status: 404 });
      }
    }

    if (action === 'markAllAsRead') {
      const count = await NotificationService.markAllAsRead(currentUser._id);
      return NextResponse.json({ success: true, markedCount: count });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Error updating notifications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
