import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get organization context for multi-tenant isolation
    const orgContext = await getOrganizationFromRequest(request);
    if (!orgContext) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    const employee = await Employee.findOne({
      email: session.user.email,
      organization: orgContext.organization._id
    }).select('-__v');

    if (!employee) {
      return NextResponse.json({ error: 'Employee not found in this organization' }, { status: 404 });
    }

    return NextResponse.json(employee);

  } catch (error) {
    console.error('Error fetching current employee:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const formData = await request.formData();
    
    const updateData: any = {};
    
    // Handle text fields
    const textFields = ['name', 'bio', 'department', 'position', 'location', 'linkedIn', 'github'];
    textFields.forEach(field => {
      const value = formData.get(field);
      if (value !== null) {
        updateData[field] = value.toString().trim();
      }
    });

    // Handle current image URL (from ImageUpload component)
    const currentImageUrl = formData.get('currentImageUrl');
    if (currentImageUrl) {
      updateData.image = currentImageUrl.toString();
    }

    // Handle array fields (skills, interests)
    const skillsValue = formData.get('skills');
    if (skillsValue) {
      updateData.skills = skillsValue.toString()
        .split(',')
        .map(skill => skill.trim())
        .filter(skill => skill.length > 0);
    }

    const interestsValue = formData.get('interests');
    if (interestsValue) {
      updateData.interests = interestsValue.toString()
        .split(',')
        .map(interest => interest.trim())
        .filter(interest => interest.length > 0);
    }

    // Handle date field
    const startDateValue = formData.get('startDate');
    if (startDateValue && startDateValue.toString().trim()) {
      updateData.startDate = new Date(startDateValue.toString());
    }

    // Remove empty strings
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === '') {
        delete updateData[key];
      }
    });

    // Get organization context for multi-tenant isolation
    const orgContext = await getOrganizationFromRequest(request);
    if (!orgContext) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    const employee = await Employee.findOneAndUpdate(
      {
        email: session.user.email,
        organization: orgContext.organization._id
      },
      updateData,
      { new: true, runValidators: true }
    ).select('-__v');

    if (!employee) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 });
    }

    return NextResponse.json(employee);

  } catch (error) {
    console.error('Error updating employee:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get organization context for multi-tenant isolation
    const orgContext = await getOrganizationFromRequest(request);
    if (!orgContext) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    const employee = await Employee.findOneAndDelete({
      email: session.user.email,
      organization: orgContext.organization._id
    });
    
    if (!employee) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Employee deleted successfully' });

  } catch (error) {
    console.error('Error deleting employee:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
