import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Organization from '@/models/Organization';
import Employee from '@/models/Employee';
import { withAuth } from '@/lib/middleware/auth';

// GET /api/organizations - List organizations (super admin only)
export const GET = withAuth(async (request: NextRequest) => {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';

    const skip = (page - 1) * limit;

    // Build query
    const query: any = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { domain: { $regex: search, $options: 'i' } },
        { subdomain: { $regex: search, $options: 'i' } }
      ];
    }

    if (status) {
      if (status === 'active') {
        query.isActive = true;
        query['subscription.status'] = 'active';
      } else if (status === 'inactive') {
        query.$or = [
          { isActive: false },
          { 'subscription.status': { $ne: 'active' } }
        ];
      }
    }

    // Get organizations with employee count
    const organizations = await Organization.find(query)
      .populate('employeeCount')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip)
      .lean();

    const total = await Organization.countDocuments(query);

    return NextResponse.json({
      organizations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasMore: skip + organizations.length < total
      }
    });

  } catch (error) {
    console.error('Error fetching organizations:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireSuperAdmin: true });

// POST /api/organizations - Create new organization (super admin only)
export const POST = withAuth(async (request: NextRequest) => {
  try {
    await dbConnect();

    const body = await request.json();
    const {
      name,
      domain,
      subdomain,
      description,
      industry,
      size,
      website,
      address,
      settings,
      subscription
    } = body;

    // Validate required fields
    if (!name || !domain) {
      return NextResponse.json(
        { error: 'Name and domain are required' },
        { status: 400 }
      );
    }

    // Check if domain already exists
    const domainQuery = { domain: domain.toLowerCase() };
    const existingDomain = await Organization.findOne(domainQuery);

    if (existingDomain) {
      return NextResponse.json(
        { error: 'Domain already exists' },
        { status: 409 }
      );
    }

    // Check if subdomain already exists (if provided)
    if (subdomain) {
      const existingSubdomain = await Organization.findOne({
        subdomain: subdomain.toLowerCase()
      });

      if (existingSubdomain) {
        return NextResponse.json(
          { error: 'Subdomain already exists' },
          { status: 409 }
        );
      }
    }

    // Create organization
    const organization = new Organization({
      name: name.trim(),
      domain: domain.toLowerCase().trim(),
      ...(subdomain && { subdomain: subdomain.toLowerCase().trim() }),
      description: description?.trim(),
      industry: industry?.trim(),
      size: size || 'small',
      website: website?.trim(),
      address: address || {},
      settings: {
        allowPublicProfiles: settings?.allowPublicProfiles ?? true,
        requireEmailVerification: settings?.requireEmailVerification ?? false,
        enableAchievements: settings?.enableAchievements ?? true,
        enableThanks: settings?.enableThanks ?? true,
        customBranding: settings?.customBranding ?? false,
        maxEmployees: settings?.maxEmployees || 50,
      },
      subscription: {
        plan: subscription?.plan || 'free',
        status: subscription?.status || 'active',
        startDate: new Date(),
        endDate: subscription?.endDate,
        features: subscription?.features || [],
      },
      isActive: true,
    });

    await organization.save();

    return NextResponse.json(organization, { status: 201 });

  } catch (error: any) {
    console.error('Error creating organization:', error);

    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'Domain already exists' },
        { status: 409 }
      );
    }

    // Handle Mongoose validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationErrors,
          message: validationErrors.join(', ')
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireSuperAdmin: true });
