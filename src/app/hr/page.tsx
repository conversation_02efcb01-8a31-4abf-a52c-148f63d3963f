'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  UserPlus,
  Building,
  Clock,
  TrendingUp,
  Activity,
  Heart,
  Trophy,
  Calendar,
  BarChart3,
  Pie<PERSON>hart,
  RefreshCw,
  Filter,
  Download
} from 'lucide-react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import Navigation from '@/components/Navigation';
import { hasPermission } from '@/lib/roles';
import { UserRole } from '@/types/global';

interface HRStats {
  totalEmployees: number;
  newHiresThisMonth: number;
  totalDepartments: number;
  averageTenure: string;
  employeesByRole: {
    role: string;
    count: number;
  }[];
  departmentStats: {
    department: string;
    employeeCount: number;
    thanksCount: number;
    achievementCount: number;
  }[];
  recentActivity: {
    type: 'hire' | 'promotion' | 'achievement' | 'thanks';
    description: string;
    date: string;
  }[];
}

export default function HRDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState<HRStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState('month');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    const userRole = session.user?.role as UserRole;
    if (!hasPermission(userRole, 'view_hr_analytics')) {
      router.push('/');
      return;
    }

    fetchHRStats();
  }, [session, status, router]);

  const fetchHRStats = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const response = await fetch('/api/analytics/hr');
      if (!response.ok) {
        throw new Error('Failed to fetch HR analytics');
      }

      const data = await response.json();
      setStats(data.stats);
      setError('');

    } catch (error) {
      console.error('Error fetching HR stats:', error);
      setError('Failed to load HR analytics');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    fetchHRStats(true);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'hire': return '👋';
      case 'promotion': return '📈';
      case 'achievement': return '🏆';
      case 'thanks': return '🙏';
      default: return '📝';
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'hire': return 'bg-green-100 text-green-800';
      case 'promotion': return 'bg-blue-100 text-blue-800';
      case 'achievement': return 'bg-yellow-100 text-yellow-800';
      case 'thanks': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };



  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
        <Navigation />
        <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col items-center justify-center h-64"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-purple-200 border-t-purple-600 rounded-full mb-4"
            />
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="text-gray-600 font-medium"
            >
              Loading HR dashboard...
            </motion.p>
          </motion.div>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <Navigation />

      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                HR Dashboard
              </h1>
              <p className="mt-2 text-gray-600 text-lg">
                Human Resources analytics and employee management overview
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <motion.select
                whileHover={{ scale: 1.02 }}
                value={selectedTimeframe}
                onChange={(e) => setSelectedTimeframe(e.target.value)}
                className="px-4 py-2 bg-white rounded-lg shadow-md border border-gray-200 text-gray-700 font-medium focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </motion.select>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center space-x-2 px-4 py-2 bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 border border-gray-200"
              >
                <RefreshCw size={18} className={`${refreshing ? 'animate-spin' : ''} text-gray-600`} />
                <span className="text-gray-700 font-medium">Refresh</span>
              </motion.button>
            </div>
          </div>
        </motion.div>

        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mb-6 bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-xl shadow-sm"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Activity className="h-5 w-5 text-red-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium">{error}</p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {stats && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-8"
          >
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[
                  {
                    title: 'Total Employees',
                    value: stats.totalEmployees,
                    icon: Users,
                    color: 'from-primary-500 to-primary-600',
                    bgColor: 'bg-primary-50',
                    change: `+${stats.newHiresThisMonth} this month`,
                    trend: 'up'
                  },
                  {
                    title: 'New Hires',
                    value: stats.newHiresThisMonth,
                    icon: UserPlus,
                    color: 'from-green-500 to-emerald-600',
                    bgColor: 'bg-green-50',
                    change: '+15% vs last month',
                    trend: 'up'
                  },
                  {
                    title: 'Departments',
                    value: stats.totalDepartments,
                    icon: Building,
                    color: 'from-purple-500 to-purple-600',
                    bgColor: 'bg-purple-50',
                    change: 'Stable',
                    trend: 'stable'
                  },
                  {
                    title: 'Avg. Tenure',
                    value: stats.averageTenure,
                    icon: Clock,
                    color: 'from-orange-500 to-yellow-600',
                    bgColor: 'bg-orange-50',
                    change: '+0.2 years',
                    trend: 'up'
                  }
                ].map((stat, index) => (
                  <motion.div
                    key={stat.title}
                    variants={itemVariants}
                    whileHover={{ scale: 1.02, y: -5 }}
                    className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
                  >
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                          <stat.icon className={`w-6 h-6 bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`} />
                        </div>
                        <div className={`flex items-center text-sm font-medium ${
                          stat.trend === 'up' ? 'text-green-600' :
                          stat.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {stat.trend === 'up' && <TrendingUp size={16} className="mr-1" />}
                          {stat.change}
                        </div>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                        <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Employees by Role */}
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                  <div className="px-4 py-5 sm:px-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Employees by Role
                    </h3>
                    <p className="mt-1 max-w-2xl text-sm text-gray-500">
                      Distribution of employees across different roles
                    </p>
                  </div>
                  <div className="px-4 py-5">
                    <div className="space-y-4">
                      {stats.employeesByRole.map((roleData) => (
                        <div key={roleData.role} className="flex items-center justify-between">
                          <div className="text-sm font-medium text-gray-900">
                            {roleData.role}
                          </div>
                          <div className="flex items-center">
                            <div className="w-24 bg-gray-200 rounded-full h-2 mr-3">
                              <div
                                className="bg-primary-500 h-2 rounded-full"
                                style={{ width: `${(roleData.count / stats.totalEmployees) * 100}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-500 w-8 text-right">{roleData.count}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                  <div className="px-4 py-5 sm:px-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Recent Activity
                    </h3>
                    <p className="mt-1 max-w-2xl text-sm text-gray-500">
                      Latest employee-related activities
                    </p>
                  </div>
                  <ul className="divide-y divide-gray-200">
                    {stats.recentActivity.map((activity, index) => (
                      <li key={index}>
                        <div className="px-4 py-4 flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <span className="text-lg">{getActivityIcon(activity.type)}</span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {activity.description}
                            </p>
                            <p className="text-sm text-gray-500">
                              {new Date(activity.date).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="flex-shrink-0">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActivityColor(activity.type)}`}>
                              {activity.type}
                            </span>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Department Statistics */}
              <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Department Statistics
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Employee count, thanks, and achievements by department
                  </p>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Department
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Employees
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Thanks
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Achievements
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {stats.departmentStats.map((dept) => (
                        <tr key={dept.department}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {dept.department}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {dept.employeeCount}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {dept.thanksCount}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {dept.achievementCount}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
