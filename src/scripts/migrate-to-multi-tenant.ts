/**
 * Migration script to convert existing data to multi-tenant structure
 * This script should be run once to migrate existing data
 */

import dbConnect from '@/lib/mongodb';
import Organization from '@/models/Organization';
import Employee from '@/models/Employee';
import Thanks from '@/models/Thanks';
import Achievement from '@/models/Achievement';
import Notification from '@/models/Notification';

async function migrateToMultiTenant() {
  try {
    console.log('🚀 Starting multi-tenant migration...');
    
    await dbConnect();

    // Step 1: Create or get demo organization
    console.log('📋 Step 1: Creating demo organization...');
    let demoOrg = await Organization.findOne({ subdomain: 'demo' });
    
    if (!demoOrg) {
      demoOrg = new Organization({
        name: 'Demo Company',
        domain: 'demo.localhost',
        subdomain: 'demo',
        description: 'Demo organization for existing data',
        industry: 'Technology',
        size: 'medium',
        settings: {
          allowPublicProfiles: true,
          requireEmailVerification: false,
          enableAchievements: true,
          enableThanks: true,
          customBranding: false,
          maxEmployees: 1000,
        },
        subscription: {
          plan: 'premium',
          status: 'active',
          startDate: new Date(),
          features: ['thanks', 'achievements', 'analytics', 'custom_branding'],
        },
        isActive: true,
      });
      
      await demoOrg.save();
      console.log('✅ Demo organization created');
    } else {
      console.log('✅ Demo organization already exists');
    }

    // Step 2: Migrate employees
    console.log('👥 Step 2: Migrating employees...');
    const employeesWithoutOrg = await Employee.find({ organization: { $exists: false } });
    console.log(`Found ${employeesWithoutOrg.length} employees without organization`);
    
    if (employeesWithoutOrg.length > 0) {
      await Employee.updateMany(
        { organization: { $exists: false } },
        { 
          $set: { 
            organization: demoOrg._id,
            isActive: true
          } 
        }
      );
      console.log(`✅ Migrated ${employeesWithoutOrg.length} employees to demo organization`);
    }

    // Step 3: Migrate thanks
    console.log('💝 Step 3: Migrating thanks...');
    const thanksWithoutOrg = await Thanks.find({ organization: { $exists: false } });
    console.log(`Found ${thanksWithoutOrg.length} thanks without organization`);
    
    if (thanksWithoutOrg.length > 0) {
      await Thanks.updateMany(
        { organization: { $exists: false } },
        { $set: { organization: demoOrg._id } }
      );
      console.log(`✅ Migrated ${thanksWithoutOrg.length} thanks to demo organization`);
    }

    // Step 4: Migrate achievements
    console.log('🏆 Step 4: Migrating achievements...');
    const achievementsWithoutOrg = await Achievement.find({ organization: { $exists: false } });
    console.log(`Found ${achievementsWithoutOrg.length} achievements without organization`);
    
    if (achievementsWithoutOrg.length > 0) {
      await Achievement.updateMany(
        { organization: { $exists: false } },
        { $set: { organization: demoOrg._id } }
      );
      console.log(`✅ Migrated ${achievementsWithoutOrg.length} achievements to demo organization`);
    }

    // Step 5: Migrate notifications
    console.log('🔔 Step 5: Migrating notifications...');
    const notificationsWithoutOrg = await Notification.find({ organization: { $exists: false } });
    console.log(`Found ${notificationsWithoutOrg.length} notifications without organization`);
    
    if (notificationsWithoutOrg.length > 0) {
      await Notification.updateMany(
        { organization: { $exists: false } },
        { $set: { organization: demoOrg._id } }
      );
      console.log(`✅ Migrated ${notificationsWithoutOrg.length} notifications to demo organization`);
    }

    // Step 6: Verify migration
    console.log('🔍 Step 6: Verifying migration...');
    const stats = {
      organizations: await Organization.countDocuments(),
      employees: await Employee.countDocuments({ organization: demoOrg._id }),
      thanks: await Thanks.countDocuments({ organization: demoOrg._id }),
      achievements: await Achievement.countDocuments({ organization: demoOrg._id }),
      notifications: await Notification.countDocuments({ organization: demoOrg._id }),
    };

    console.log('📊 Migration Statistics:');
    console.log(`  Organizations: ${stats.organizations}`);
    console.log(`  Employees in demo org: ${stats.employees}`);
    console.log(`  Thanks in demo org: ${stats.thanks}`);
    console.log(`  Achievements in demo org: ${stats.achievements}`);
    console.log(`  Notifications in demo org: ${stats.notifications}`);

    console.log('🎉 Multi-tenant migration completed successfully!');
    console.log('');
    console.log('🔗 Access your application at:');
    console.log('  - http://localhost:3000 (will auto-create demo org)');
    console.log('  - http://demo.localhost:3000 (if you have subdomain setup)');
    console.log('');
    console.log('📝 Next steps:');
    console.log('  1. Test the application with existing data');
    console.log('  2. Create new organizations via API or admin panel');
    console.log('  3. Set up custom domains for organizations');
    console.log('  4. Configure DNS for subdomain routing');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateToMultiTenant()
    .then(() => {
      console.log('Migration completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export default migrateToMultiTenant;
