'use client';

import { useEffect } from 'react';

export default function InputTextFixer() {
  useEffect(() => {
    // Function to force dark text color on all inputs
    const forceInputTextColor = () => {
      const inputs = document.querySelectorAll('input, textarea, select');
      inputs.forEach((input) => {
        const element = input as HTMLElement;
        // Force dark text color with maximum priority
        element.style.setProperty('color', '#000000', 'important');
        element.style.setProperty('-webkit-text-fill-color', '#000000', 'important');
        element.style.setProperty('text-shadow', 'none', 'important');
        element.style.setProperty('opacity', '1', 'important');
      });
    };

    // Run immediately
    forceInputTextColor();

    // Run when DOM changes (for dynamically added inputs)
    const observer = new MutationObserver(() => {
      forceInputTextColor();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Run on input events to ensure text stays visible while typing
    const handleInput = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {
        target.style.setProperty('color', '#000000', 'important');
        target.style.setProperty('-webkit-text-fill-color', '#000000', 'important');
        target.style.setProperty('text-shadow', 'none', 'important');
        target.style.setProperty('opacity', '1', 'important');
      }
    };

    // Add event listeners for all input events
    document.addEventListener('input', handleInput);
    document.addEventListener('focus', handleInput);
    document.addEventListener('blur', handleInput);
    document.addEventListener('change', handleInput);
    document.addEventListener('keyup', handleInput);
    document.addEventListener('keydown', handleInput);

    // Cleanup
    return () => {
      observer.disconnect();
      document.removeEventListener('input', handleInput);
      document.removeEventListener('focus', handleInput);
      document.removeEventListener('blur', handleInput);
      document.removeEventListener('change', handleInput);
      document.removeEventListener('keyup', handleInput);
      document.removeEventListener('keydown', handleInput);
    };
  }, []);

  return null; // This component doesn't render anything
}
