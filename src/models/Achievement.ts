import mongoose, { Document, Schema } from 'mongoose';

export interface IAchievement extends Document {
  _id: string;
  organization: mongoose.Types.ObjectId;
  employee: mongoose.Types.ObjectId;
  title: string;
  description: string;
  type: 'milestone' | 'recognition' | 'skill' | 'project' | 'anniversary' | 'custom';
  category: string;
  dateEarned: Date;
  isPublic: boolean;
  aiGenerated: boolean;
  metadata: {
    thanksCount?: number;
    projectName?: string;
    skillLevel?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    yearsOfService?: number;
    [key: string]: any;
  };
  badge: {
    icon: string;
    color: string;
    rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  };
  createdAt: Date;
  updatedAt: Date;
}

const AchievementSchema = new Schema<IAchievement>(
  {
    organization: {
      type: Schema.Types.ObjectId,
      ref: 'Organization',
      required: [true, 'Organization is required'],
    },
    employee: {
      type: Schema.Types.ObjectId,
      ref: 'Employee',
      required: [true, 'Employee is required'],
    },
    title: {
      type: String,
      required: [true, 'Title is required'],
      trim: true,
      maxlength: [100, 'Title cannot be more than 100 characters'],
    },
    description: {
      type: String,
      required: [true, 'Description is required'],
      trim: true,
      maxlength: [300, 'Description cannot be more than 300 characters'],
    },
    type: {
      type: String,
      enum: ['milestone', 'recognition', 'skill', 'project', 'anniversary', 'custom'],
      required: true,
    },
    category: {
      type: String,
      required: [true, 'Category is required'],
      trim: true,
      maxlength: [50, 'Category cannot be more than 50 characters'],
    },
    dateEarned: {
      type: Date,
      default: Date.now,
      required: true,
    },
    isPublic: {
      type: Boolean,
      default: true,
    },
    aiGenerated: {
      type: Boolean,
      default: false,
    },
    metadata: {
      type: Schema.Types.Mixed,
      default: {},
    },
    badge: {
      icon: {
        type: String,
        required: true,
        default: '🏆',
      },
      color: {
        type: String,
        required: true,
        default: '#FFD700',
      },
      rarity: {
        type: String,
        enum: ['common', 'uncommon', 'rare', 'epic', 'legendary'],
        default: 'common',
      },
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for better performance and multi-tenancy
// Compound indexes for organization-scoped queries
AchievementSchema.index({ organization: 1, employee: 1, dateEarned: -1 });
AchievementSchema.index({ organization: 1, employee: 1, type: 1 });
AchievementSchema.index({ organization: 1, type: 1, dateEarned: -1 });
AchievementSchema.index({ organization: 1, category: 1, dateEarned: -1 });
AchievementSchema.index({ organization: 1, isPublic: 1, dateEarned: -1 });
AchievementSchema.index({ organization: 1, 'badge.rarity': 1 });

// Single field indexes
// Note: organization index covered by compound indexes above
AchievementSchema.index({ dateEarned: -1 });

export default mongoose.models.Achievement || mongoose.model<IAchievement>('Achievement', AchievementSchema);
