import mongoose, { Document, Schema } from 'mongoose';

export interface IThanks extends Document {
  _id: string;
  organization: mongoose.Types.ObjectId;
  fromEmployee?: mongoose.Types.ObjectId;
  toEmployee: mongoose.Types.ObjectId;
  anonymousSender?: {
    name: string;
  };
  message: string;
  category: 'teamwork' | 'leadership' | 'innovation' | 'helpfulness' | 'quality' | 'other';
  isPublic: boolean;
  aiGenerated: boolean;
  reactions: {
    emoji: string;
    employees: mongoose.Types.ObjectId[];
  }[];
  createdAt: Date;
  updatedAt: Date;
}

const ThanksSchema = new Schema<IThanks>(
  {
    organization: {
      type: Schema.Types.ObjectId,
      ref: 'Organization',
      required: [true, 'Organization is required'],
    },
    fromEmployee: {
      type: Schema.Types.ObjectId,
      ref: 'Employee',
      required: false, // Not required for anonymous thanks
    },
    anonymousSender: {
      name: {
        type: String,
        trim: true,
        maxlength: [100, 'Anonymous sender name cannot be more than 100 characters'],
      }
    },
    toEmployee: {
      type: Schema.Types.ObjectId,
      ref: 'Employee',
      required: [true, 'To employee is required'],
    },
    message: {
      type: String,
      required: [true, 'Message is required'],
      trim: true,
      minlength: [10, 'Message must be at least 10 characters'],
      maxlength: [500, 'Message cannot be more than 500 characters'],
    },
    category: {
      type: String,
      enum: ['teamwork', 'leadership', 'innovation', 'helpfulness', 'quality', 'other'],
      default: 'other',
      required: true,
    },
    isPublic: {
      type: Boolean,
      default: true,
    },
    aiGenerated: {
      type: Boolean,
      default: false,
    },
    reactions: [{
      emoji: {
        type: String,
        required: true,
      },
      employees: [{
        type: Schema.Types.ObjectId,
        ref: 'Employee',
      }],
    }],
  },
  {
    timestamps: true,
  }
);

// Create indexes for better performance and multi-tenancy
// Compound indexes for organization-scoped queries
ThanksSchema.index({ organization: 1, toEmployee: 1, createdAt: -1 });
ThanksSchema.index({ organization: 1, fromEmployee: 1, createdAt: -1 });
ThanksSchema.index({ organization: 1, category: 1, createdAt: -1 });
ThanksSchema.index({ organization: 1, isPublic: 1, createdAt: -1 });

// Single field indexes
// Note: organization index covered by compound indexes above
ThanksSchema.index({ createdAt: -1 });

// Validation for thanks
ThanksSchema.pre('save', function(next) {
  // Ensure either fromEmployee or anonymousSender is provided
  if (!this.fromEmployee && !this.anonymousSender?.name) {
    next(new Error('Either fromEmployee or anonymousSender name is required'));
    return;
  }

  // Prevent self-thanks for authenticated users
  if (this.fromEmployee && this.fromEmployee.equals(this.toEmployee)) {
    next(new Error('Cannot send thanks to yourself'));
    return;
  }

  next();
});

export default mongoose.models.Thanks || mongoose.model<IThanks>('Thanks', ThanksSchema);
