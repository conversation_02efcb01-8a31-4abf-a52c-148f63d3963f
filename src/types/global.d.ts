import mongoose from 'mongoose';
import { DefaultSession, DefaultUser } from 'next-auth';
import { JWT, DefaultJWT } from 'next-auth/jwt';

declare global {
  var mongoose: {
    conn: typeof mongoose | null;
    promise: Promise<typeof mongoose> | null;
  };
}

// Role and Permission Types
export type UserRole = 'employee' | 'manager' | 'hr' | 'admin' | 'super_admin';

export type Permission =
  // Employee permissions
  | 'view_own_profile'
  | 'edit_own_profile'
  | 'view_public_profiles'
  | 'send_thanks'
  | 'view_public_thanks'
  | 'view_own_achievements'

  // Manager permissions
  | 'view_team_profiles'
  | 'edit_team_profiles'
  | 'view_team_thanks'
  | 'create_team_achievements'
  | 'view_team_analytics'

  // HR permissions
  | 'view_all_profiles'
  | 'edit_all_profiles'
  | 'create_employees'
  | 'view_all_thanks'
  | 'create_achievements'
  | 'view_hr_analytics'
  | 'manage_departments'

  // Admin permissions
  | 'manage_users'
  | 'manage_roles'
  | 'view_system_analytics'
  | 'manage_system_settings'
  | 'delete_users'
  | 'backup_data'

  // Super Admin permissions
  | 'manage_organizations'
  | 'view_all_organizations'
  | 'manage_super_admins'
  | 'system_configuration'
  | 'cross_organization_access';

export interface RolePermissions {
  [key: string]: Permission[];
}

// NextAuth type extensions
declare module 'next-auth' {
  interface Session extends DefaultSession {
    user: {
      id: string;
      role: UserRole;
      department?: string;
      position?: string;
    } & DefaultSession['user'];
  }

  interface User extends DefaultUser {
    id: string;
    role: UserRole;
    department?: string;
    position?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT extends DefaultJWT {
    id: string;
    role: UserRole;
    department?: string;
    position?: string;
  }
}

export {};
