/**
 * Client-side image compression utilities
 * Compresses images in the browser before upload to ensure they're under 100KB
 */

interface CompressionOptions {
  maxSizeKB: number;
  maxWidth: number;
  maxHeight: number;
  quality: number;
  format: 'jpeg' | 'png' | 'webp';
}

interface CompressionResult {
  file: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  width: number;
  height: number;
}

/**
 * Compress image file in the browser
 */
export async function compressImageFile(
  file: File,
  options: Partial<CompressionOptions> = {}
): Promise<CompressionResult> {
  const opts: CompressionOptions = {
    maxSizeKB: 100,
    maxWidth: 800,
    maxHeight: 800,
    quality: 85,
    format: 'jpeg',
    ...options,
  };

  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      try {
        const result = compressImage(img, file, opts);
        resolve(result);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = URL.createObjectURL(file);
  });
}

/**
 * Compress image using canvas
 */
function compressImage(
  img: HTMLImageElement,
  originalFile: File,
  options: CompressionOptions
): Promise<CompressionResult> {
  return new Promise((resolve, reject) => {
    // Calculate new dimensions
    const { width: newWidth, height: newHeight } = calculateDimensions(
      img.width,
      img.height,
      options.maxWidth,
      options.maxHeight
    );

    // Create canvas
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      reject(new Error('Could not get canvas context'));
      return;
    }

    canvas.width = newWidth;
    canvas.height = newHeight;

    // Draw image on canvas
    ctx.drawImage(img, 0, 0, newWidth, newHeight);

    // Try different quality levels until we get under the size limit
    let quality = options.quality / 100;
    let attempts = 0;
    const maxAttempts = 10;

    const tryCompress = () => {
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to create blob'));
            return;
          }

          const sizeKB = blob.size / 1024;
          
          if (sizeKB <= options.maxSizeKB || attempts >= maxAttempts) {
            // Create final file
            const compressedFile = new File(
              [blob],
              generateFileName(originalFile.name, options.format),
              { type: `image/${options.format}` }
            );

            const compressionRatio = Math.round((1 - blob.size / originalFile.size) * 100);

            resolve({
              file: compressedFile,
              originalSize: originalFile.size,
              compressedSize: blob.size,
              compressionRatio,
              width: newWidth,
              height: newHeight,
            });
          } else {
            // Reduce quality and try again
            quality = Math.max(0.1, quality - 0.1);
            attempts++;
            tryCompress();
          }
        },
        `image/${options.format}`,
        quality
      );
    };

    tryCompress();
  });
}

/**
 * Calculate new dimensions while maintaining aspect ratio
 */
function calculateDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  const aspectRatio = originalWidth / originalHeight;

  let newWidth = originalWidth;
  let newHeight = originalHeight;

  // Scale down if too wide
  if (newWidth > maxWidth) {
    newWidth = maxWidth;
    newHeight = newWidth / aspectRatio;
  }

  // Scale down if too tall
  if (newHeight > maxHeight) {
    newHeight = maxHeight;
    newWidth = newHeight * aspectRatio;
  }

  return {
    width: Math.round(newWidth),
    height: Math.round(newHeight),
  };
}

/**
 * Generate filename with new extension
 */
function generateFileName(originalName: string, format: string): string {
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
  const timestamp = Date.now();
  return `${nameWithoutExt}-compressed-${timestamp}.${format}`;
}

/**
 * Validate image file before compression
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxSizeMB = 10; // 10MB max before compression

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.',
    };
  }

  if (file.size > maxSizeMB * 1024 * 1024) {
    return {
      valid: false,
      error: `File too large. Please upload an image smaller than ${maxSizeMB}MB.`,
    };
  }

  return { valid: true };
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Create image preview URL
 */
export function createImagePreview(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * Cleanup image preview URL
 */
export function cleanupImagePreview(url: string): void {
  URL.revokeObjectURL(url);
}
