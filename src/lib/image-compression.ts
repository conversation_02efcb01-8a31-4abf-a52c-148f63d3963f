/**
 * Image compression utilities
 * Ensures images are under 100KB while maintaining quality
 */

interface CompressionOptions {
  maxSizeKB: number;
  maxWidth: number;
  maxHeight: number;
  quality: number;
  format: 'jpeg' | 'png' | 'webp';
}

interface CompressionResult {
  buffer: Buffer;
  size: number;
  width: number;
  height: number;
  format: string;
}

/**
 * Compress image to meet size requirements
 */
export async function compressImage(
  inputBuffer: Buffer,
  options: Partial<CompressionOptions> = {}
): Promise<CompressionResult> {
  // Default options
  const opts: CompressionOptions = {
    maxSizeKB: 100,
    maxWidth: 800,
    maxHeight: 800,
    quality: 85,
    format: 'jpeg',
    ...options,
  };

  try {
    // For now, we'll use a simple approach with canvas-based compression
    // In a production environment, you might want to use sharp or similar
    const compressed = await compressImageBrowser(inputBuffer, opts);
    return compressed;
  } catch (error) {
    console.error('Error compressing image:', error);
    throw new Error('Failed to compress image');
  }
}

/**
 * Browser-compatible image compression using Canvas API
 */
async function compressImageBrowser(
  inputBuffer: Buffer,
  options: CompressionOptions
): Promise<CompressionResult> {
  return new Promise((resolve, reject) => {
    try {
      // Create a blob from the buffer
      const blob = new Blob([inputBuffer]);
      const img = new Image();
      
      img.onload = () => {
        try {
          // Calculate new dimensions
          const { width: newWidth, height: newHeight } = calculateDimensions(
            img.width,
            img.height,
            options.maxWidth,
            options.maxHeight
          );

          // Create canvas
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            reject(new Error('Could not get canvas context'));
            return;
          }

          canvas.width = newWidth;
          canvas.height = newHeight;

          // Draw and compress
          ctx.drawImage(img, 0, 0, newWidth, newHeight);

          // Try different quality levels until we get under the size limit
          let quality = options.quality / 100;
          let attempts = 0;
          const maxAttempts = 10;

          const tryCompress = () => {
            canvas.toBlob(
              (blob) => {
                if (!blob) {
                  reject(new Error('Failed to create blob'));
                  return;
                }

                const sizeKB = blob.size / 1024;
                
                if (sizeKB <= options.maxSizeKB || attempts >= maxAttempts) {
                  // Convert blob to buffer
                  blob.arrayBuffer().then(arrayBuffer => {
                    const buffer = Buffer.from(arrayBuffer);
                    resolve({
                      buffer,
                      size: blob.size,
                      width: newWidth,
                      height: newHeight,
                      format: options.format,
                    });
                  });
                } else {
                  // Reduce quality and try again
                  quality = Math.max(0.1, quality - 0.1);
                  attempts++;
                  tryCompress();
                }
              },
              `image/${options.format}`,
              quality
            );
          };

          tryCompress();
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = URL.createObjectURL(blob);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Server-side image compression using sharp (if available)
 */
export async function compressImageServer(
  inputBuffer: Buffer,
  options: CompressionOptions
): Promise<CompressionResult> {
  try {
    // Try to use sharp if available
    const sharp = await import('sharp').catch(() => null);
    
    if (!sharp) {
      throw new Error('Sharp not available for server-side compression');
    }

    let pipeline = sharp.default(inputBuffer);
    
    // Resize if needed
    pipeline = pipeline.resize(options.maxWidth, options.maxHeight, {
      fit: 'inside',
      withoutEnlargement: true,
    });

    // Apply format and quality
    switch (options.format) {
      case 'jpeg':
        pipeline = pipeline.jpeg({ quality: options.quality });
        break;
      case 'png':
        pipeline = pipeline.png({ quality: options.quality });
        break;
      case 'webp':
        pipeline = pipeline.webp({ quality: options.quality });
        break;
    }

    const { data, info } = await pipeline.toBuffer({ resolveWithObject: true });
    
    // Check if we need to reduce quality further
    if (data.length / 1024 > options.maxSizeKB && options.quality > 10) {
      return compressImageServer(inputBuffer, {
        ...options,
        quality: Math.max(10, options.quality - 10),
      });
    }

    return {
      buffer: data,
      size: data.length,
      width: info.width,
      height: info.height,
      format: info.format,
    };
  } catch (error) {
    console.error('Server-side compression failed:', error);
    throw error;
  }
}

/**
 * Calculate new dimensions while maintaining aspect ratio
 */
function calculateDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  const aspectRatio = originalWidth / originalHeight;

  let newWidth = originalWidth;
  let newHeight = originalHeight;

  // Scale down if too wide
  if (newWidth > maxWidth) {
    newWidth = maxWidth;
    newHeight = newWidth / aspectRatio;
  }

  // Scale down if too tall
  if (newHeight > maxHeight) {
    newHeight = maxHeight;
    newWidth = newHeight * aspectRatio;
  }

  return {
    width: Math.round(newWidth),
    height: Math.round(newHeight),
  };
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxSizeMB = 10; // 10MB max before compression

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.',
    };
  }

  if (file.size > maxSizeMB * 1024 * 1024) {
    return {
      valid: false,
      error: `File too large. Please upload an image smaller than ${maxSizeMB}MB.`,
    };
  }

  return { valid: true };
}

/**
 * Get file extension from MIME type
 */
export function getFileExtension(mimeType: string): string {
  switch (mimeType) {
    case 'image/jpeg':
    case 'image/jpg':
      return 'jpg';
    case 'image/png':
      return 'png';
    case 'image/gif':
      return 'gif';
    case 'image/webp':
      return 'webp';
    default:
      return 'jpg';
  }
}
