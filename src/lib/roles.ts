/**
 * Role-Based Access Control (RBAC) System
 * Defines user roles, permissions, and access control logic
 */

import { UserRole, Permission, RolePermissions } from '@/types/global';

// Define permissions for each role
export const ROLE_PERMISSIONS: RolePermissions = {
  employee: [
    'view_own_profile',
    'edit_own_profile',
    'view_public_profiles',
    'send_thanks',
    'view_public_thanks',
    'view_own_achievements',
  ],
  manager: [
    // Include all employee permissions
    'view_own_profile',
    'edit_own_profile',
    'view_public_profiles',
    'send_thanks',
    'view_public_thanks',
    'view_own_achievements',
    // Manager-specific permissions
    'view_team_profiles',
    'edit_team_profiles',
    'view_team_thanks',
    'create_team_achievements',
    'view_team_analytics',
  ],
  hr: [
    // Include all employee permissions
    'view_own_profile',
    'edit_own_profile',
    'view_public_profiles',
    'send_thanks',
    'view_public_thanks',
    'view_own_achievements',
    // HR-specific permissions
    'view_all_profiles',
    'edit_all_profiles',
    'create_employees',
    'view_all_thanks',
    'create_achievements',
    'view_hr_analytics',
    'manage_departments',
  ],
  admin: [
    // Include all permissions except super admin specific
    'view_own_profile',
    'edit_own_profile',
    'view_public_profiles',
    'send_thanks',
    'view_public_thanks',
    'view_own_achievements',
    'view_team_profiles',
    'edit_team_profiles',
    'view_team_thanks',
    'create_team_achievements',
    'view_team_analytics',
    'view_all_profiles',
    'edit_all_profiles',
    'create_employees',
    'view_all_thanks',
    'create_achievements',
    'view_hr_analytics',
    'manage_departments',
    // Admin-specific permissions (organization-scoped)
    'manage_users',
    'manage_roles',
    'view_system_analytics',
    'manage_system_settings',
    'delete_users',
    'backup_data',
  ],
  super_admin: [
    // Include all permissions
    'view_own_profile',
    'edit_own_profile',
    'view_public_profiles',
    'send_thanks',
    'view_public_thanks',
    'view_own_achievements',
    'view_team_profiles',
    'edit_team_profiles',
    'view_team_thanks',
    'create_team_achievements',
    'view_team_analytics',
    'view_all_profiles',
    'edit_all_profiles',
    'create_employees',
    'view_all_thanks',
    'create_achievements',
    'view_hr_analytics',
    'manage_departments',
    'manage_users',
    'manage_roles',
    'view_system_analytics',
    'manage_system_settings',
    'delete_users',
    'backup_data',
    // Super Admin-specific permissions (cross-organization)
    'manage_organizations',
    'view_all_organizations',
    'manage_super_admins',
    'system_configuration',
    'cross_organization_access',
  ],
};

// Role hierarchy (higher number = more permissions)
export const ROLE_HIERARCHY: Record<UserRole, number> = {
  employee: 1,
  manager: 2,
  hr: 3,
  admin: 4,
  super_admin: 5,
};

// Role display names
export const ROLE_DISPLAY_NAMES: Record<UserRole, string> = {
  employee: 'Employee',
  manager: 'Manager',
  hr: 'HR Representative',
  admin: 'Administrator',
  super_admin: 'Super Administrator',
};

// Role descriptions
export const ROLE_DESCRIPTIONS: Record<UserRole, string> = {
  employee: 'Basic access to view profiles, send thanks, and manage own profile',
  manager: 'Can manage team members, view team analytics, and create team achievements',
  hr: 'Can manage all employee profiles, departments, and view comprehensive analytics',
  admin: 'Full organization access including user management, role assignment, and organization settings',
  super_admin: 'System-wide access across all organizations with ability to manage organizations and super admins',
};

/**
 * Check if a user has a specific permission
 */
export function hasPermission(userRole: UserRole, permission: Permission): boolean {
  const rolePermissions = ROLE_PERMISSIONS[userRole];
  return rolePermissions.includes(permission);
}

/**
 * Check if a user has any of the specified permissions
 */
export function hasAnyPermission(userRole: UserRole, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(userRole, permission));
}

/**
 * Check if a user has all of the specified permissions
 */
export function hasAllPermissions(userRole: UserRole, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(userRole, permission));
}

/**
 * Check if a role is higher or equal in hierarchy than another role
 */
export function isRoleHigherOrEqual(userRole: UserRole, targetRole: UserRole): boolean {
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[targetRole];
}

/**
 * Check if a role is higher in hierarchy than another role
 */
export function isRoleHigher(userRole: UserRole, targetRole: UserRole): boolean {
  return ROLE_HIERARCHY[userRole] > ROLE_HIERARCHY[targetRole];
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role] || [];
}

/**
 * Get all available roles
 */
export function getAllRoles(): UserRole[] {
  return Object.keys(ROLE_PERMISSIONS) as UserRole[];
}

/**
 * Check if a user can manage another user based on role hierarchy
 */
export function canManageUser(managerRole: UserRole, targetRole: UserRole): boolean {
  // Super admins can manage everyone including other super admins
  if (managerRole === 'super_admin') return true;

  // Admins can manage everyone except super admins
  if (managerRole === 'admin' && targetRole !== 'super_admin') return true;

  // HR can manage employees and managers
  if (managerRole === 'hr' && (targetRole === 'employee' || targetRole === 'manager')) return true;

  // Managers can manage employees
  if (managerRole === 'manager' && targetRole === 'employee') return true;

  return false;
}

/**
 * Get roles that a user can assign to others
 */
export function getAssignableRoles(userRole: UserRole): UserRole[] {
  switch (userRole) {
    case 'super_admin':
      return ['employee', 'manager', 'hr', 'admin', 'super_admin'];
    case 'admin':
      return ['employee', 'manager', 'hr', 'admin'];
    case 'hr':
      return ['employee', 'manager'];
    case 'manager':
      return ['employee'];
    default:
      return [];
  }
}

/**
 * Validate if a role is valid
 */
export function isValidRole(role: string): role is UserRole {
  return getAllRoles().includes(role as UserRole);
}

/**
 * Get default role for new users
 */
export function getDefaultRole(): UserRole {
  return 'employee';
}
