import Notification, { INotification } from '@/models/Notification';
import Employee from '@/models/Employee';
import dbConnect from '@/lib/mongodb';

export interface CreateNotificationParams {
  organizationId: string;
  recipientId: string;
  senderId: string;
  type: 'thanks_received' | 'achievement_earned' | 'mention' | 'system';
  title: string;
  message: string;
  relatedId?: string;
  relatedType?: 'thanks' | 'achievement' | 'other';
  metadata?: Record<string, any>;
}

export interface NotificationPreferences {
  emailNotifications: boolean;
  inAppNotifications: boolean;
  thanksNotifications: boolean;
  achievementNotifications: boolean;
  mentionNotifications: boolean;
}

export class NotificationService {
  /**
   * Create a new notification
   */
  static async createNotification(params: CreateNotificationParams): Promise<INotification | null> {
    try {
      await dbConnect();

      // Check if recipient exists and get their preferences
      const recipient = await Employee.findById(params.recipientId);
      if (!recipient) {
        console.error('Recipient not found:', params.recipientId);
        return null;
      }

      // Check notification preferences (if implemented)
      const preferences = recipient.notificationPreferences || {
        emailNotifications: true,
        inAppNotifications: true,
        thanksNotifications: true,
        achievementNotifications: true,
        mentionNotifications: true
      };

      // Check if user wants this type of notification
      if (!preferences.inAppNotifications) {
        return null;
      }

      if (params.type === 'thanks_received' && !preferences.thanksNotifications) {
        return null;
      }

      if (params.type === 'achievement_earned' && !preferences.achievementNotifications) {
        return null;
      }

      if (params.type === 'mention' && !preferences.mentionNotifications) {
        return null;
      }

      // Create the notification
      const notification = new Notification({
        organization: params.organizationId,
        recipient: params.recipientId,
        sender: params.senderId,
        type: params.type,
        title: params.title,
        message: params.message,
        relatedId: params.relatedId,
        relatedType: params.relatedType,
        metadata: params.metadata || {}
      });

      await notification.save();

      // Populate sender details for immediate use
      await notification.populate('sender', 'name email image');

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      return null;
    }
  }

  /**
   * Create notification for received thanks
   */
  static async createThanksNotification(
    thanksId: string,
    recipientId: string,
    senderId: string,
    senderName: string,
    category: string,
    organizationId: string
  ): Promise<INotification | null> {
    const categoryEmojis: Record<string, string> = {
      teamwork: '🤝',
      leadership: '👑',
      innovation: '⚡',
      helpfulness: '💝',
      quality: '⭐',
      other: '🎯'
    };

    const emoji = categoryEmojis[category] || '💝';

    return this.createNotification({
      organizationId,
      recipientId,
      senderId,
      type: 'thanks_received',
      title: `${emoji} Thanks from ${senderName}`,
      message: `${senderName} sent you thanks for ${category}! Check it out to see their message.`,
      relatedId: thanksId,
      relatedType: 'thanks',
      metadata: {
        thanksCategory: category,
        senderName
      }
    });
  }

  /**
   * Create notification for earned achievement
   */
  static async createAchievementNotification(
    achievementId: string,
    recipientId: string,
    achievementTitle: string,
    achievementType: string
  ): Promise<INotification | null> {
    return this.createNotification({
      recipientId,
      senderId: recipientId, // Self-notification for achievements
      type: 'achievement_earned',
      title: `🏆 Achievement Unlocked!`,
      message: `Congratulations! You've earned the "${achievementTitle}" achievement.`,
      relatedId: achievementId,
      relatedType: 'achievement',
      metadata: {
        achievementTitle,
        achievementType
      }
    });
  }

  /**
   * Get notifications for a user
   */
  static async getUserNotifications(
    userId: string,
    options: {
      organizationId?: string;
      limit?: number;
      page?: number;
      unreadOnly?: boolean;
      type?: string;
    } = {}
  ): Promise<{
    notifications: INotification[];
    total: number;
    unreadCount: number;
    hasMore: boolean;
  }> {
    try {
      await dbConnect();

      const { organizationId, limit = 20, page = 1, unreadOnly = false, type } = options;
      const skip = (page - 1) * limit;

      // Build query
      const query: any = { recipient: userId };
      if (organizationId) {
        query.organization = organizationId;
      }
      if (unreadOnly) {
        query.isRead = false;
      }
      if (type) {
        query.type = type;
      }

      // Get notifications
      const notifications = await Notification.find(query)
        .populate('sender', 'name email image')
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(skip)
        .lean();

      // Get total count
      const total = await Notification.countDocuments(query);

      // Get unread count
      const unreadQuery: any = {
        recipient: userId,
        isRead: false
      };
      if (organizationId) {
        unreadQuery.organization = organizationId;
      }
      const unreadCount = await Notification.countDocuments(unreadQuery);

      return {
        notifications: notifications as INotification[],
        total,
        unreadCount,
        hasMore: skip + notifications.length < total
      };
    } catch (error) {
      console.error('Error getting user notifications:', error);
      return {
        notifications: [],
        total: 0,
        unreadCount: 0,
        hasMore: false
      };
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId: string, userId: string): Promise<boolean> {
    try {
      await dbConnect();

      const result = await Notification.updateOne(
        { _id: notificationId, recipient: userId },
        { isRead: true }
      );

      return result.modifiedCount > 0;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllAsRead(userId: string): Promise<number> {
    try {
      await dbConnect();

      const result = await Notification.updateMany(
        { recipient: userId, isRead: false },
        { isRead: true }
      );

      return result.modifiedCount;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return 0;
    }
  }

  /**
   * Delete old notifications (cleanup)
   */
  static async cleanupOldNotifications(daysOld: number = 90): Promise<number> {
    try {
      await dbConnect();

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const result = await Notification.deleteMany({
        createdAt: { $lt: cutoffDate }
      });

      return result.deletedCount || 0;
    } catch (error) {
      console.error('Error cleaning up old notifications:', error);
      return 0;
    }
  }
}

export default NotificationService;
