/**
 * Authentication and Authorization Middleware
 * Provides role-based access control for API routes
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UserRole, Permission } from '@/types/global';
import { hasPermission, hasAnyPermission, canManageUser } from '@/lib/roles';

export interface AuthenticatedRequest extends NextRequest {
  user: {
    id: string;
    email: string;
    name: string;
    role: UserRole;
    department?: string;
    position?: string;
  };
}

/**
 * Middleware to check if user is authenticated
 */
export async function requireAuth(request: NextRequest): Promise<NextResponse | null> {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.email) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
  }
  
  // Attach user info to request for downstream use
  (request as AuthenticatedRequest).user = {
    id: session.user.id,
    email: session.user.email,
    name: session.user.name || '',
    role: session.user.role,
    department: session.user.department,
    position: session.user.position,
  };
  
  return null; // Continue to next middleware/handler
}

/**
 * Middleware to check if user has required role
 */
export async function requireRole(
  request: NextRequest,
  requiredRole: UserRole
): Promise<NextResponse | null> {
  const authResult = await requireAuth(request);
  if (authResult) return authResult;
  
  const user = (request as AuthenticatedRequest).user;
  
  if (user.role !== requiredRole) {
    return NextResponse.json(
      { error: `Access denied. ${requiredRole} role required.` },
      { status: 403 }
    );
  }
  
  return null;
}

/**
 * Middleware to check if user has any of the required roles
 */
export async function requireAnyRole(
  request: NextRequest,
  requiredRoles: UserRole[]
): Promise<NextResponse | null> {
  const authResult = await requireAuth(request);
  if (authResult) return authResult;
  
  const user = (request as AuthenticatedRequest).user;
  
  if (!requiredRoles.includes(user.role)) {
    return NextResponse.json(
      { error: `Access denied. One of these roles required: ${requiredRoles.join(', ')}` },
      { status: 403 }
    );
  }
  
  return null;
}

/**
 * Middleware to check if user has required permission
 */
export async function requirePermission(
  request: NextRequest,
  requiredPermission: Permission
): Promise<NextResponse | null> {
  const authResult = await requireAuth(request);
  if (authResult) return authResult;
  
  const user = (request as AuthenticatedRequest).user;
  
  if (!hasPermission(user.role, requiredPermission)) {
    return NextResponse.json(
      { error: `Access denied. Permission '${requiredPermission}' required.` },
      { status: 403 }
    );
  }
  
  return null;
}

/**
 * Middleware to check if user has any of the required permissions
 */
export async function requireAnyPermission(
  request: NextRequest,
  requiredPermissions: Permission[]
): Promise<NextResponse | null> {
  const authResult = await requireAuth(request);
  if (authResult) return authResult;
  
  const user = (request as AuthenticatedRequest).user;
  
  if (!hasAnyPermission(user.role, requiredPermissions)) {
    return NextResponse.json(
      { error: `Access denied. One of these permissions required: ${requiredPermissions.join(', ')}` },
      { status: 403 }
    );
  }
  
  return null;
}

/**
 * Middleware to check if user can manage another user
 */
export async function requireUserManagement(
  request: NextRequest,
  targetUserRole: UserRole
): Promise<NextResponse | null> {
  const authResult = await requireAuth(request);
  if (authResult) return authResult;
  
  const user = (request as AuthenticatedRequest).user;
  
  if (!canManageUser(user.role, targetUserRole)) {
    return NextResponse.json(
      { error: 'Access denied. Insufficient privileges to manage this user.' },
      { status: 403 }
    );
  }
  
  return null;
}

/**
 * Middleware to check if user is admin
 */
export async function requireAdmin(request: NextRequest): Promise<NextResponse | null> {
  return requireRole(request, 'admin');
}

/**
 * Middleware to check if user is HR or admin
 */
export async function requireHROrAdmin(request: NextRequest): Promise<NextResponse | null> {
  return requireAnyRole(request, ['hr', 'admin']);
}

/**
 * Middleware to check if user is manager, HR, or admin
 */
export async function requireManagerOrAbove(request: NextRequest): Promise<NextResponse | null> {
  return requireAnyRole(request, ['manager', 'hr', 'admin', 'super_admin']);
}

/**
 * Middleware to check if user is super admin
 */
export async function requireSuperAdmin(request: NextRequest): Promise<NextResponse | null> {
  return requireRole(request, 'super_admin');
}

/**
 * Middleware to check if user is admin or super admin
 */
export async function requireAdminOrSuperAdmin(request: NextRequest): Promise<NextResponse | null> {
  return requireAnyRole(request, ['admin', 'super_admin']);
}

/**
 * Get authenticated user from request
 */
export function getAuthenticatedUser(request: NextRequest) {
  return (request as AuthenticatedRequest).user;
}

/**
 * Check if current user can access another user's data
 */
export function canAccessUserData(
  currentUser: { id: string; role: UserRole; department?: string; organization?: string },
  targetUserId: string,
  targetUserRole?: UserRole,
  targetUserDepartment?: string,
  targetUserOrganization?: string
): boolean {
  // Users can always access their own data
  if (currentUser.id === targetUserId) {
    return true;
  }

  // Super admins can access anyone's data across all organizations
  if (currentUser.role === 'super_admin') {
    return true;
  }

  // For other roles, check organization scope first
  if (currentUser.organization && targetUserOrganization &&
      currentUser.organization !== targetUserOrganization) {
    return false; // Cannot access users from different organizations
  }

  // Admins can access anyone's data within their organization
  if (currentUser.role === 'admin') {
    return true;
  }

  // HR can access all employee data within their organization
  if (currentUser.role === 'hr') {
    return true;
  }

  // Managers can access their team members' data (same department and organization)
  if (currentUser.role === 'manager' &&
      currentUser.department &&
      targetUserDepartment === currentUser.department &&
      targetUserRole === 'employee') {
    return true;
  }

  return false;
}

/**
 * Middleware wrapper for easy use in API routes
 */
export function withAuth(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>,
  options?: {
    roles?: UserRole[];
    permissions?: Permission[];
    requireAdmin?: boolean;
    requireSuperAdmin?: boolean;
    requireAdminOrSuperAdmin?: boolean;
    requireHROrAdmin?: boolean;
    requireManagerOrAbove?: boolean;
  }
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    // Check authentication first
    const authResult = await requireAuth(request);
    if (authResult) return authResult;

    // Check specific requirements
    if (options?.requireSuperAdmin) {
      const superAdminResult = await requireSuperAdmin(request);
      if (superAdminResult) return superAdminResult;
    }

    if (options?.requireAdminOrSuperAdmin) {
      const adminOrSuperResult = await requireAdminOrSuperAdmin(request);
      if (adminOrSuperResult) return adminOrSuperResult;
    }

    if (options?.requireAdmin) {
      const adminResult = await requireAdmin(request);
      if (adminResult) return adminResult;
    }

    if (options?.requireHROrAdmin) {
      const hrResult = await requireHROrAdmin(request);
      if (hrResult) return hrResult;
    }

    if (options?.requireManagerOrAbove) {
      const managerResult = await requireManagerOrAbove(request);
      if (managerResult) return managerResult;
    }

    if (options?.roles) {
      const roleResult = await requireAnyRole(request, options.roles);
      if (roleResult) return roleResult;
    }

    if (options?.permissions) {
      const permissionResult = await requireAnyPermission(request, options.permissions);
      if (permissionResult) return permissionResult;
    }

    // All checks passed, call the handler with context (which includes params)
    return handler(request, context);
  };
}
